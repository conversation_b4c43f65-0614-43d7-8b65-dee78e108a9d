"""
AI Agent for the Smart Kitchen Queue Management System using Ollama and LangChain.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    from langchain_community.llms import Ollama
except ImportError:
    try:
        from langchain.llms import Ollama
    except ImportError:
        # Mock Ollama for testing
        class Ollama:
            def __init__(self, **kwargs):
                self.kwargs = kwargs

            def __call__(self, prompt):
                return "Mock AI response for testing"

from app.config import config
from app.services.ai_tools import AIToolsManager
from app.services.excel_service import ExcelDataService
from app.services.queue_manager import KitchenQueueManager
from app.services.starvation_prevention import StarvationPrevention
from app.models import QueueItem, PriorityLevel, ItemStatus, ScheduleOptimizationResult

logger = logging.getLogger(__name__)


class KitchenAIAgent:
    """AI Agent for intelligent kitchen queue management and scheduling."""
    
    def __init__(self, excel_service: ExcelDataService, queue_manager: KitchenQueueManager,
                 starvation_prevention: StarvationPrevention):
        """Initialize the AI agent."""
        self.excel_service = excel_service
        self.queue_manager = queue_manager
        self.starvation_prevention = starvation_prevention
        
        # Initialize Ollama LLM
        try:
            self.llm = Ollama(
                base_url=config.OLLAMA_BASE_URL,
                model=config.OLLAMA_MODEL,
                temperature=0.1  # Low temperature for consistent scheduling decisions
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Ollama: {e}. Using mock AI.")
            self.llm = Ollama()  # Mock version

        # Initialize AI tools
        self.tools_manager = AIToolsManager(
            excel_service, queue_manager, starvation_prevention
        )

        # Simple conversation history
        self.conversation_history = []
        
        logger.info("Kitchen AI Agent initialized successfully")
    
    def optimize_order_schedule(self, order_items: List[str], order_id: str) -> ScheduleOptimizationResult:
        """Optimize scheduling for a new order using AI decision making."""
        try:
            # Use AI tools directly for optimization
            order_analyzer = self.tools_manager.order_analyzer
            kitchen_assessor = self.tools_manager.kitchen_load_assessor
            scheduler = self.tools_manager.scheduling_optimizer
            starvation_monitor = self.tools_manager.anti_starvation_monitor

            # Step 1: Analyze order
            order_analysis = order_analyzer._run(order_items, order_id)

            # Step 2: Assess kitchen loads
            kitchen_loads = kitchen_assessor._run(include_predictions=True)

            # Step 3: Check for starvation
            starvation_check = starvation_monitor._run(order_items, order_id)

            # Step 4: Optimize scheduling
            import json
            kitchen_loads_dict = json.loads(kitchen_loads) if isinstance(kitchen_loads, str) else kitchen_loads
            optimization = scheduler._run(order_items, order_id, kitchen_loads_dict)

            # Parse the optimization result
            optimization_result = self._parse_tool_response(optimization, order_items, order_id)

            logger.info(f"AI optimization completed for order {order_id}")
            return optimization_result

        except Exception as e:
            logger.error(f"Error in AI optimization for order {order_id}: {e}")
            # Fallback to basic scheduling
            return self._fallback_scheduling(order_items, order_id)
    
    def _create_scheduling_prompt(self, order_items: List[str], order_id: str) -> str:
        """Create the scheduling prompt for the AI agent."""
        
        # Get current system status
        kitchen_statuses = self.queue_manager.get_all_kitchen_statuses()
        starvation_stats = self.starvation_prevention.get_starvation_statistics()
        
        # Format kitchen status information
        kitchen_status_info = []
        for status in kitchen_statuses:
            kitchen_status_info.append(
                f"Kitchen {status.kitchen_id} ({status.kitchen_name}): "
                f"Load {status.current_load}/{status.capacity}, "
                f"Available slots: {status.available_slots}, "
                f"Next available: {status.next_available_time.strftime('%H:%M')}"
            )
        
        # Format starvation information
        starving_items = starvation_stats.get("emergency_items", [])
        starvation_info = f"Currently starving items: {len(starving_items)}"
        if starving_items:
            starvation_info += f" - Items: {', '.join(starving_items)}"
        
        prompt = f"""
You are an intelligent kitchen queue management system. Your task is to optimize food preparation scheduling across multiple kitchens.

CURRENT STATUS:
{chr(10).join(kitchen_status_info)}

NEW ORDER: {order_id}
Items to schedule: {', '.join(order_items)}

STARVATION STATUS: {starvation_info}
Total delayed items: {starvation_stats.get('total_delayed_items', 0)}

OPTIMIZATION RULES:
1. Minimize total order completion time
2. Ensure items finish within 2-3 minutes of each other for synchronized delivery
3. Utilize kitchen capacities efficiently without overloading
4. CRITICAL: Prevent item starvation - if an item has been delayed {config.MAX_STARVATION_COUNT}+ times, give it EMERGENCY priority
5. Consider historical prep time variations and kitchen efficiency
6. Balance kitchen loads to prevent bottlenecks

REQUIRED ACTIONS:
1. Use the order_analyzer tool to validate items and get kitchen assignments
2. Use the kitchen_load_assessor tool to get detailed current loads
3. Use the anti_starvation_monitor tool to check for starving items
4. Use the scheduling_optimizer tool to create the optimal schedule

Please analyze this order and provide an optimized schedule that follows all rules and prevents starvation.
Focus on efficiency while ensuring no item is left behind.

Return your final recommendation in JSON format with:
- optimized_schedule: detailed schedule for each kitchen
- synchronization_achieved: boolean
- anti_starvation_applied: list of items with emergency priority
- total_estimated_time: minutes until all items complete
- optimization_notes: explanation of decisions made
"""
        
        return prompt
    
    def _parse_tool_response(self, response: str, order_items: List[str], order_id: str) -> ScheduleOptimizationResult:
        """Parse tool response and create optimization result."""
        try:
            import json
            if isinstance(response, str):
                # Try to extract JSON from the response
                json_start = response.find('{')
                json_end = response.rfind('}') + 1

                if json_start != -1 and json_end > json_start:
                    json_str = response[json_start:json_end]
                    tool_result = json.loads(json_str)
                else:
                    # Fallback if no JSON found
                    logger.warning("No JSON found in tool response, using fallback")
                    return self._fallback_scheduling(order_items, order_id)
            else:
                tool_result = response

            # Create queue items from tool result
            optimized_items = []
            anti_starvation_applied = tool_result.get("anti_starvation_applied", [])

            # Use fallback scheduling with tool insights
            return self._fallback_scheduling(order_items, order_id, tool_result)

        except Exception as e:
            logger.error(f"Error parsing tool response: {e}")
            return self._fallback_scheduling(order_items, order_id)

    def _parse_ai_response(self, response: str, order_items: List[str], order_id: str) -> ScheduleOptimizationResult:
        """Parse AI response and create optimization result."""
        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                ai_result = json.loads(json_str)
            else:
                # Fallback if no JSON found
                logger.warning("No JSON found in AI response, using fallback")
                return self._fallback_scheduling(order_items, order_id)
            
            # Create queue items from AI schedule
            optimized_items = []
            anti_starvation_applied = ai_result.get("anti_starvation_applied", [])
            
            for kitchen_id, kitchen_schedule in ai_result.get("optimized_schedule", {}).items():
                for item_schedule in kitchen_schedule:
                    item_id = item_schedule["item_id"]
                    
                    # Determine priority
                    priority = PriorityLevel.NORMAL
                    if item_id in anti_starvation_applied:
                        priority = PriorityLevel.EMERGENCY
                    elif item_schedule.get("priority") == "high":
                        priority = PriorityLevel.HIGH
                    elif item_schedule.get("priority") == "low":
                        priority = PriorityLevel.LOW
                    
                    # Create queue item
                    queue_item = QueueItem(
                        item_id=item_id,
                        item_name=item_schedule["item_name"],
                        order_id=order_id,
                        prep_time=int(item_schedule["prep_time"]),
                        scheduled_start=datetime.fromisoformat(item_schedule["scheduled_start"]),
                        estimated_completion=datetime.fromisoformat(item_schedule["estimated_completion"]),
                        priority_level=priority,
                        starvation_count=self.starvation_prevention.get_item_delay_info(item_id)["delay_count"],
                        status=ItemStatus.QUEUED,
                        kitchen_id=kitchen_id
                    )
                    
                    optimized_items.append(queue_item)
            
            return ScheduleOptimizationResult(
                order_id=order_id,
                optimized_items=optimized_items,
                total_estimated_time=ai_result.get("total_estimated_time", 0),
                synchronization_achieved=ai_result.get("synchronization_achieved", False),
                anti_starvation_applied=anti_starvation_applied,
                optimization_notes=ai_result.get("optimization_notes", "AI optimization completed")
            )
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return self._fallback_scheduling(order_items, order_id)
    
    def _fallback_scheduling(self, order_items: List[str], order_id: str, tool_insights: dict = None) -> ScheduleOptimizationResult:
        """Fallback scheduling when AI fails."""
        logger.warning(f"Using fallback scheduling for order {order_id}")
        
        try:
            menu_items = self.excel_service.load_menu_items()
            optimized_items = []
            current_time = datetime.now()
            
            for item_id in order_items:
                item = menu_items[item_id]
                kitchen_id = item['kitchen_id']
                
                # Check for starvation
                priority = PriorityLevel.NORMAL
                if self.starvation_prevention.check_starvation(item_id):
                    priority = PriorityLevel.EMERGENCY
                
                # Get next available slot
                start_time = self.queue_manager.get_next_available_slot(kitchen_id)
                completion_time = start_time + timedelta(minutes=item['prep_time_minutes'])
                
                queue_item = QueueItem(
                    item_id=item_id,
                    item_name=item['item_name'],
                    order_id=order_id,
                    prep_time=item['prep_time_minutes'],
                    scheduled_start=start_time,
                    estimated_completion=completion_time,
                    priority_level=priority,
                    starvation_count=self.starvation_prevention.get_item_delay_info(item_id)["delay_count"],
                    status=ItemStatus.QUEUED,
                    kitchen_id=kitchen_id
                )
                
                optimized_items.append(queue_item)
            
            # Calculate total time
            if optimized_items:
                total_time = max(
                    (item.estimated_completion - current_time).total_seconds() / 60
                    for item in optimized_items
                )
            else:
                total_time = 0
            
            return ScheduleOptimizationResult(
                order_id=order_id,
                optimized_items=optimized_items,
                total_estimated_time=int(total_time),
                synchronization_achieved=False,
                anti_starvation_applied=[],
                optimization_notes="Fallback scheduling used due to AI error"
            )
            
        except Exception as e:
            logger.error(f"Error in fallback scheduling: {e}")
            raise

    def reoptimize_pending_orders(self) -> List[ScheduleOptimizationResult]:
        """Reoptimize all pending orders when system state changes."""
        try:
            # Get all pending orders from queue manager
            all_statuses = self.queue_manager.get_all_kitchen_statuses()
            pending_orders = {}

            # Group queue items by order
            for status in all_statuses:
                for item in status.current_queue:
                    if item.status == ItemStatus.QUEUED:
                        if item.order_id not in pending_orders:
                            pending_orders[item.order_id] = []
                        pending_orders[item.order_id].append(item.item_id)

            # Reoptimize each pending order
            reoptimization_results = []
            for order_id, items in pending_orders.items():
                result = self.optimize_order_schedule(items, order_id)
                reoptimization_results.append(result)

            logger.info(f"Reoptimized {len(pending_orders)} pending orders")
            return reoptimization_results

        except Exception as e:
            logger.error(f"Error reoptimizing pending orders: {e}")
            return []

    def get_ai_insights(self) -> Dict[str, Any]:
        """Get AI insights about current system performance."""
        try:
            insights_prompt = """
Analyze the current kitchen system performance and provide insights.

Use the kitchen_load_assessor tool to get current status and the anti_starvation_monitor
tool to check system-wide starvation issues.

Provide insights on:
1. Current system efficiency
2. Bottleneck identification
3. Starvation prevention effectiveness
4. Recommendations for improvement

Return insights in JSON format.
"""

            # Use tools directly for insights
            kitchen_assessor = self.tools_manager.kitchen_load_assessor
            starvation_monitor = self.tools_manager.anti_starvation_monitor

            kitchen_loads = kitchen_assessor._run(include_predictions=True)
            starvation_status = starvation_monitor._run([], "SYSTEM_INSIGHTS")

            response = f"Kitchen loads: {kitchen_loads}\nStarvation status: {starvation_status}"

            # Try to extract JSON insights
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                return {
                    "status": "analysis_completed",
                    "raw_response": response,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Error getting AI insights: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def update_learning_data(self, performance_data: Dict):
        """Update the AI's learning data with new performance information."""
        try:
            # Store performance data in Excel
            self.excel_service.update_historical_performance(performance_data)

            # Clear conversation history to incorporate new learning
            self.conversation_history.clear()

            logger.info("AI learning data updated successfully")

        except Exception as e:
            logger.error(f"Error updating AI learning data: {e}")

    def emergency_reschedule(self, starving_items: List[str]) -> Dict[str, Any]:
        """Emergency rescheduling for starving items."""
        try:
            emergency_prompt = f"""
EMERGENCY RESCHEDULING REQUIRED

The following items are experiencing starvation and need immediate attention:
{', '.join(starving_items)}

Use all available tools to:
1. Assess current kitchen loads
2. Apply emergency priority to starving items
3. Reschedule to prevent further delays

This is a CRITICAL situation - prioritize these items above all others.
Return emergency schedule in JSON format.
"""

            # Use starvation monitor tool directly
            starvation_monitor = self.tools_manager.anti_starvation_monitor
            response = starvation_monitor._run(starving_items, "EMERGENCY_RESCHEDULE")

            # Parse emergency response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                emergency_result = json.loads(json_str)
            else:
                emergency_result = {
                    "status": "emergency_processed",
                    "raw_response": response
                }

            emergency_result["timestamp"] = datetime.now().isoformat()
            emergency_result["starving_items"] = starving_items

            logger.critical(f"Emergency rescheduling completed for {len(starving_items)} items")
            return emergency_result

        except Exception as e:
            logger.error(f"Error in emergency rescheduling: {e}")
            return {
                "status": "error",
                "error": str(e),
                "starving_items": starving_items,
                "timestamp": datetime.now().isoformat()
            }
