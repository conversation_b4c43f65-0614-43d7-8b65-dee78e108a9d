"""
Real-time API endpoints for the Smart Kitchen Queue Management System.
"""

import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Request

from app.models import (
    QueueStatusResponse, PerformanceMetricsResponse,
    PerformanceQueryRequest, PerformanceHistoryResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_services(request: Request):
    """Dependency to get services from app state."""
    return {
        'excel_service': request.app.state.excel_service,
        'queue_manager': request.app.state.queue_manager,
        'starvation_prevention': request.app.state.starvation_prevention,
        'ai_agent': request.app.state.ai_agent,
        'performance_learning': request.app.state.performance_learning
    }


@router.get("/queue", response_model=QueueStatusResponse)
async def get_queue_status(services=Depends(get_services)):
    """Get current queue status across all kitchens."""
    try:
        kitchen_statuses = services['queue_manager'].get_all_kitchen_statuses()
        
        # Collect all queue items
        all_queue_items = []
        kitchen_loads = {}
        
        for status in kitchen_statuses:
            all_queue_items.extend(status.current_queue)
            kitchen_loads[status.kitchen_id] = status.current_load
        
        # Filter out completed items for the response
        active_queue_items = [
            item for item in all_queue_items 
            if item.status.value != "completed"
        ]
        
        return QueueStatusResponse(
            success=True,
            message=f"Queue status retrieved for {len(kitchen_statuses)} kitchens",
            queue_items=active_queue_items,
            total_items=len(active_queue_items),
            kitchen_loads=kitchen_loads
        )
        
    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(services=Depends(get_services)):
    """Get current system performance metrics."""
    try:
        # Get starvation statistics
        starvation_stats = services['starvation_prevention'].get_starvation_statistics()
        
        # Get kitchen efficiency metrics
        kitchen_statuses = services['queue_manager'].get_all_kitchen_statuses()
        kitchen_metrics = {}
        
        for status in kitchen_statuses:
            efficiency = services['queue_manager'].get_kitchen_efficiency_metrics(status.kitchen_id)
            kitchen_metrics[status.kitchen_id] = {
                "current_load": status.current_load,
                "capacity": status.capacity,
                "utilization": (status.current_load / status.capacity * 100) if status.capacity > 0 else 0,
                "efficiency_metrics": efficiency
            }
        
        # Calculate overall system metrics
        total_capacity = sum(status.capacity for status in kitchen_statuses)
        total_load = sum(status.current_load for status in kitchen_statuses)
        overall_utilization = (total_load / total_capacity * 100) if total_capacity > 0 else 0
        
        metrics = {
            "system_overview": {
                "total_kitchens": len(kitchen_statuses),
                "total_capacity": total_capacity,
                "total_current_load": total_load,
                "overall_utilization_percentage": round(overall_utilization, 2),
                "timestamp": datetime.now().isoformat()
            },
            "starvation_metrics": starvation_stats,
            "kitchen_metrics": kitchen_metrics,
            "bottlenecks": services['performance_learning'].identify_bottlenecks()
        }
        
        return PerformanceMetricsResponse(
            success=True,
            message="Performance metrics retrieved successfully",
            metrics=metrics
        )
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/performance/history", response_model=PerformanceHistoryResponse)
async def get_performance_history(
    query: PerformanceQueryRequest,
    services=Depends(get_services)
):
    """Get historical performance data with filtering."""
    try:
        historical_data = services['excel_service'].load_historical_data()
        
        # Apply filters
        filtered_data = historical_data
        
        if query.start_date:
            filtered_data = filtered_data[filtered_data['date'] >= query.start_date]
        
        if query.end_date:
            filtered_data = filtered_data[filtered_data['date'] <= query.end_date]
        
        if query.kitchen_id:
            filtered_data = filtered_data[filtered_data['kitchen_id'] == query.kitchen_id]
        
        if query.item_id:
            filtered_data = filtered_data[filtered_data['item_id'] == query.item_id]
        
        # Apply limit
        filtered_data = filtered_data.head(query.limit)
        
        # Convert to PerformanceRecord objects
        records = []
        for _, row in filtered_data.iterrows():
            records.append({
                "date": row['date'],
                "kitchen_id": row['kitchen_id'],
                "item_id": row['item_id'],
                "order_id": row['order_id'],
                "actual_prep_time": int(row['actual_prep_time']),
                "scheduled_prep_time": int(row['scheduled_prep_time']),
                "delay_minutes": int(row['delay_minutes']),
                "kitchen_load": int(row['kitchen_load'])
            })
        
        return PerformanceHistoryResponse(
            success=True,
            message=f"Retrieved {len(records)} performance records",
            records=records,
            total_count=len(records)
        )
        
    except Exception as e:
        logger.error(f"Error getting performance history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/starvation", response_model=dict)
async def get_starvation_status(services=Depends(get_services)):
    """Get current starvation status and statistics."""
    try:
        starvation_stats = services['starvation_prevention'].get_starvation_statistics()
        starving_items = services['starvation_prevention'].get_starving_items()
        
        # Get detailed info for each starving item
        starving_details = []
        for item_id in starving_items:
            item_info = services['starvation_prevention'].get_item_delay_info(item_id)
            starving_details.append(item_info)
        
        return {
            "success": True,
            "message": "Starvation status retrieved successfully",
            "statistics": starvation_stats,
            "starving_items_details": starving_details,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting starvation status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reoptimize", response_model=dict)
async def trigger_reoptimization(services=Depends(get_services)):
    """Trigger reoptimization of all pending orders."""
    try:
        results = services['ai_agent'].reoptimize_pending_orders()
        
        return {
            "success": True,
            "message": f"Reoptimized {len(results)} pending orders",
            "reoptimization_results": [
                {
                    "order_id": result.order_id,
                    "total_estimated_time": result.total_estimated_time,
                    "synchronization_achieved": result.synchronization_achieved,
                    "anti_starvation_applied": result.anti_starvation_applied
                }
                for result in results
            ],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error triggering reoptimization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/insights", response_model=dict)
async def get_ai_insights(services=Depends(get_services)):
    """Get AI-generated insights about system performance."""
    try:
        insights = services['ai_agent'].get_ai_insights()
        
        return {
            "success": True,
            "message": "AI insights retrieved successfully",
            "insights": insights,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting AI insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/emergency-reschedule", response_model=dict)
async def emergency_reschedule(services=Depends(get_services)):
    """Trigger emergency rescheduling for starving items."""
    try:
        starving_items = services['starvation_prevention'].get_starving_items()
        
        if not starving_items:
            return {
                "success": True,
                "message": "No starving items found - no emergency rescheduling needed",
                "timestamp": datetime.now().isoformat()
            }
        
        emergency_result = services['ai_agent'].emergency_reschedule(starving_items)
        
        return {
            "success": True,
            "message": f"Emergency rescheduling completed for {len(starving_items)} items",
            "emergency_result": emergency_result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in emergency rescheduling: {e}")
        raise HTTPException(status_code=500, detail=str(e))
