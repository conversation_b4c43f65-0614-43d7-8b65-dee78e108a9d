"""
Orders API endpoints for the Smart Kitchen Queue Management System.
"""

import logging
import uuid
from typing import List
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse

from app.models import (
    CreateOrderRequest, UpdateOrderRequest, OrderResponse, OrderListResponse,
    Order, OrderStatus, ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_services(request: Request):
    """Dependency to get services from app state."""
    return {
        'excel_service': request.app.state.excel_service,
        'queue_manager': request.app.state.queue_manager,
        'starvation_prevention': request.app.state.starvation_prevention,
        'ai_agent': request.app.state.ai_agent,
        'performance_learning': request.app.state.performance_learning
    }


# In-memory order storage (in production, use a proper database)
orders_storage = {}


@router.post("/", response_model=OrderResponse)
async def create_order(order_request: CreateOrderRequest, services=Depends(get_services)):
    """Create a new order and optimize its schedule."""
    try:
        # Generate unique order ID
        order_id = f"ORD_{uuid.uuid4().hex[:8].upper()}"
        
        # Validate items
        if not services['excel_service'].validate_order_items(order_request.items):
            raise HTTPException(
                status_code=400,
                detail="One or more items are invalid or unavailable"
            )
        
        # Create order
        order = Order(
            order_id=order_id,
            items=order_request.items,
            timestamp=datetime.now(),
            status=OrderStatus.PENDING
        )
        
        # Use AI agent to optimize schedule
        optimization_result = services['ai_agent'].optimize_order_schedule(
            order_request.items, order_id
        )
        
        # Update order with optimization results
        order.queue_items = optimization_result.optimized_items
        order.total_prep_time = optimization_result.total_estimated_time
        order.status = OrderStatus.PROCESSING
        
        # Calculate estimated completion time
        if optimization_result.optimized_items:
            order.estimated_completion = max(
                item.estimated_completion for item in optimization_result.optimized_items
            )
        
        # Add items to kitchen queues
        for queue_item in optimization_result.optimized_items:
            success = services['queue_manager'].add_item_to_queue(
                queue_item.kitchen_id, queue_item
            )
            if not success:
                logger.warning(f"Failed to add item {queue_item.item_id} to queue")
        
        # Store order
        orders_storage[order_id] = order
        
        logger.info(f"Created order {order_id} with {len(order_request.items)} items")
        
        return OrderResponse(
            success=True,
            message=f"Order {order_id} created successfully",
            order=order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating order: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(order_id: str, services=Depends(get_services)):
    """Get order details by ID."""
    try:
        if order_id not in orders_storage:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        order = orders_storage[order_id]
        
        # Update order status based on queue items
        if order.queue_items:
            completed_items = sum(1 for item in order.queue_items if item.status.value == "completed")
            total_items = len(order.queue_items)
            
            if completed_items == total_items:
                order.status = OrderStatus.COMPLETED
                if not order.actual_completion:
                    order.actual_completion = datetime.now()
            elif completed_items > 0:
                order.status = OrderStatus.PROCESSING
        
        return OrderResponse(
            success=True,
            message=f"Order {order_id} retrieved successfully",
            order=order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving order {order_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=OrderListResponse)
async def list_orders(
    status: str = None,
    limit: int = 50,
    offset: int = 0,
    services=Depends(get_services)
):
    """List orders with optional filtering."""
    try:
        orders_list = list(orders_storage.values())
        
        # Filter by status if provided
        if status:
            orders_list = [order for order in orders_list if order.status.value == status]
        
        # Sort by timestamp (newest first)
        orders_list.sort(key=lambda x: x.timestamp, reverse=True)
        
        # Apply pagination
        total_count = len(orders_list)
        orders_list = orders_list[offset:offset + limit]
        
        return OrderListResponse(
            success=True,
            message=f"Retrieved {len(orders_list)} orders",
            orders=orders_list,
            total_count=total_count
        )
        
    except Exception as e:
        logger.error(f"Error listing orders: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{order_id}", response_model=OrderResponse)
async def update_order(
    order_id: str,
    update_request: UpdateOrderRequest,
    services=Depends(get_services)
):
    """Update an existing order."""
    try:
        if order_id not in orders_storage:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        order = orders_storage[order_id]
        
        # Update status if provided
        if update_request.status:
            try:
                new_status = OrderStatus(update_request.status)
                order.status = new_status
                
                if new_status == OrderStatus.COMPLETED:
                    order.actual_completion = datetime.now()
                    
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status: {update_request.status}"
                )
        
        logger.info(f"Updated order {order_id}")
        
        return OrderResponse(
            success=True,
            message=f"Order {order_id} updated successfully",
            order=order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating order {order_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{order_id}/cancel", response_model=OrderResponse)
async def cancel_order(order_id: str, services=Depends(get_services)):
    """Cancel an existing order."""
    try:
        if order_id not in orders_storage:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        order = orders_storage[order_id]
        
        if order.status == OrderStatus.COMPLETED:
            raise HTTPException(
                status_code=400,
                detail="Cannot cancel a completed order"
            )
        
        # Cancel all queue items
        for queue_item in order.queue_items:
            if queue_item.status.value != "completed":
                services['queue_manager'].update_item_status(
                    queue_item.kitchen_id,
                    queue_item.item_id,
                    "cancelled"
                )
        
        order.status = OrderStatus.CANCELLED
        
        logger.info(f"Cancelled order {order_id}")
        
        return OrderResponse(
            success=True,
            message=f"Order {order_id} cancelled successfully",
            order=order
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling order {order_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
