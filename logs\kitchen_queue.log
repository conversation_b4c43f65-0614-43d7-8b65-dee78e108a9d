2025-07-14 12:24:48,418 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:24:48,419 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:24:48,420 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:24:48,421 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:24:48,422 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:25:42,118 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:25:42,119 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:25:42,119 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:25:42,120 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:25:42,121 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:25:46,450 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:25:46,702 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:25:46,703 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:25:46,704 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:25:46,705 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:25:46,707 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:25:46,708 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:49:45,877 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:49:45,879 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:49:45,880 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:49:45,881 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:49:45,882 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:49:51,724 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:49:52,263 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:49:52,268 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:49:52,276 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:49:52,279 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:49:52,283 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:49:52,285 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:53:13,465 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:53:13,466 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:53:13,467 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:53:13,468 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:53:13,469 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:53:16,963 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:53:17,120 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:53:17,121 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:53:17,122 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:53:17,123 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:53:17,124 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:53:17,124 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:54:00,993 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:54:00,994 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:54:00,995 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:54:00,996 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:54:00,996 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:54:05,715 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:54:06,123 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:54:06,124 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:54:06,125 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:54:06,127 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:54:06,130 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:54:06,132 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:55:30,926 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:55:30,933 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:55:30,937 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:55:30,938 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:55:30,940 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:04,410 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:56:04,411 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:56:04,412 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:56:04,413 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:56:04,413 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:26,019 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:56:26,020 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:56:26,020 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:56:26,020 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:56:26,021 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:29,263 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:56:29,433 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:56:29,434 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:56:29,434 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:56:29,435 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:56:29,436 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:56:29,437 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
