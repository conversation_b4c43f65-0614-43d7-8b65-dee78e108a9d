2025-07-14 12:24:48,418 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:24:48,419 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:24:48,420 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:24:48,421 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:24:48,422 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:25:42,118 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:25:42,119 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:25:42,119 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:25:42,120 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:25:42,121 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:25:46,450 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:25:46,702 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:25:46,703 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:25:46,704 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:25:46,705 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:25:46,707 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:25:46,708 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:49:45,877 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:49:45,879 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:49:45,880 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:49:45,881 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:49:45,882 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:49:51,724 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:49:52,263 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:49:52,268 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:49:52,276 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:49:52,279 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:49:52,283 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:49:52,285 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:53:13,465 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:53:13,466 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:53:13,467 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:53:13,468 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:53:13,469 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:53:16,963 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:53:17,120 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:53:17,121 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:53:17,122 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:53:17,123 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:53:17,124 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:53:17,124 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:54:00,993 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:54:00,994 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:54:00,995 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:54:00,996 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:54:00,996 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:54:05,715 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:54:06,123 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:54:06,124 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:54:06,125 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:54:06,127 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:54:06,130 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:54:06,132 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:55:30,926 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:55:30,933 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:55:30,937 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:55:30,938 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:55:30,940 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:04,410 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:56:04,411 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:56:04,412 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:56:04,413 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:56:04,413 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:26,019 - __main__ - INFO - Starting Smart Kitchen Queue Management System
2025-07-14 12:56:26,020 - __main__ - INFO - Configuration validated successfully
2025-07-14 12:56:26,020 - __main__ - INFO - Excel file path: data/kitchen_config.xlsx
2025-07-14 12:56:26,020 - __main__ - INFO - Ollama URL: http://ollama:11434
2025-07-14 12:56:26,021 - __main__ - INFO - API will run on 0.0.0.0:8000
2025-07-14 12:56:29,263 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:56:29,433 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:56:29,434 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:56:29,434 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:56:29,435 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:56:29,436 - app.main - ERROR - Failed to initialize services: "OrderAnalyzerTool" object has no field "excel_service"
2025-07-14 12:56:29,437 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:58:07,704 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:58:07,902 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:58:07,903 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:58:07,904 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:58:07,905 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:58:07,907 - app.main - ERROR - Failed to initialize services: "KitchenLoadAssessor" object has no field "queue_manager"
2025-07-14 12:58:07,908 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:58:50,000 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:58:50,167 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:58:50,168 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:58:50,168 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:58:50,169 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:58:50,172 - app.main - ERROR - Failed to initialize services: "SchedulingOptimizer" object has no field "excel_service"
2025-07-14 12:58:50,173 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 12:59:18,515 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 12:59:18,685 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 12:59:18,686 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 12:59:18,687 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 12:59:18,688 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 12:59:18,689 - app.main - ERROR - Failed to initialize services: "AntiStarvationMonitor" object has no field "starvation_prevention"
2025-07-14 12:59:18,690 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:00:44,499 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:00:44,702 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:00:44,703 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:00:44,704 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:00:44,705 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:00:44,707 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:00:44,718 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:00:44,719 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:13:40,541 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:13:40,784 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:13:40,785 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:13:40,785 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:13:40,786 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:13:40,788 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:13:40,797 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:13:40,798 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:14:08,950 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:14:09,282 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:14:09,283 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:14:09,284 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:14:09,285 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:14:09,287 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:14:09,305 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:14:09,306 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:14:25,859 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:14:26,057 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:14:26,058 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:14:26,059 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:14:26,060 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:14:26,061 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:14:26,070 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:14:26,071 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:14:46,405 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:14:46,602 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:14:46,603 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:14:46,604 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:14:46,606 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:14:46,610 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:14:46,639 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:14:46,640 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:18:25,017 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:18:25,180 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:18:25,181 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:18:25,182 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:18:25,182 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:18:25,184 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:18:25,192 - app.main - ERROR - Failed to initialize services: ConversationalChatAgent does not support multi-input tool order_analyzer.
2025-07-14 13:18:25,193 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:27:05,589 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:27:06,004 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:27:06,005 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:27:06,006 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:27:06,007 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:27:06,011 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:27:06,018 - app.main - ERROR - Failed to initialize services: name 'ConversationBufferMemory' is not defined
2025-07-14 13:27:06,019 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:28:37,841 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:28:38,057 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:28:38,057 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:28:38,058 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:28:38,058 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:28:38,060 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:28:38,060 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:28:38,061 - app.main - INFO - All services initialized successfully
2025-07-14 13:28:58,751 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:29:01,143 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:29:01,348 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:29:01,349 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:29:01,350 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:29:01,351 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:29:01,352 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:29:01,353 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:29:01,353 - app.main - INFO - All services initialized successfully
2025-07-14 13:29:38,819 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:29:41,152 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:29:41,365 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:29:41,365 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:29:41,366 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:29:41,367 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:29:41,369 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:29:41,370 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:29:41,371 - app.main - INFO - All services initialized successfully
2025-07-14 13:29:52,218 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:29:54,613 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:29:54,819 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:29:54,820 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:29:54,820 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:29:54,821 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:29:54,822 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:29:54,823 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:29:54,823 - app.main - INFO - All services initialized successfully
2025-07-14 13:30:09,701 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:30:12,507 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:30:12,747 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:30:12,748 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:30:12,749 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:30:12,750 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:30:12,751 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:30:12,752 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:30:12,753 - app.main - INFO - All services initialized successfully
2025-07-14 13:30:25,732 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:30:27,898 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:30:28,098 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:30:28,099 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:30:28,100 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:30:28,100 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:30:28,102 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:30:28,102 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:30:28,103 - app.main - INFO - All services initialized successfully
2025-07-14 13:30:42,974 - app.main - INFO - Shutting down Smart Kitchen Queue Management System
2025-07-14 13:30:45,241 - app.main - INFO - Initializing Smart Kitchen Queue Management System...
2025-07-14 13:30:45,446 - app.services.excel_service - INFO - Loaded 3 kitchens from Excel
2025-07-14 13:30:45,447 - app.services.queue_manager - INFO - Loaded configurations for 3 kitchens
2025-07-14 13:30:45,448 - app.services.starvation_prevention - INFO - Starvation prevention initialized with max_delays=3
2025-07-14 13:30:45,449 - app.services.performance_learning - INFO - Performance Learning System initialized
2025-07-14 13:30:45,451 - app.services.ai_tools - INFO - AI Tools Manager initialized with all tools
2025-07-14 13:30:45,451 - app.services.ai_agent - INFO - Kitchen AI Agent initialized successfully
2025-07-14 13:30:45,452 - app.main - INFO - All services initialized successfully
2025-07-15 04:43:57,644 - app.services.excel_service - INFO - Loaded 12 menu items from Excel
2025-07-15 04:43:57,648 - app.services.ai_tools - INFO - Analyzed order ORD_A72D7AF5: 3 kitchens, 58 min total
2025-07-15 04:43:57,669 - app.services.excel_service - INFO - Loaded kitchen load patterns from Excel
2025-07-15 04:43:57,676 - app.services.ai_tools - INFO - Kitchen load assessment: 0/9 capacity used
2025-07-15 04:43:57,677 - app.services.ai_tools - INFO - Starvation check for order ORD_A72D7AF5: 0 starving items
2025-07-15 04:43:57,713 - app.services.excel_service - INFO - Loaded 100 historical records from Excel
2025-07-15 04:43:57,717 - app.services.ai_tools - ERROR - Error optimizing schedule for order ORD_A72D7AF5: unsupported operand type(s) for -: 'str' and 'datetime.datetime'
2025-07-15 04:43:57,718 - app.services.ai_agent - WARNING - Using fallback scheduling for order ORD_A72D7AF5
2025-07-15 04:43:57,720 - app.services.ai_agent - INFO - AI optimization completed for order ORD_A72D7AF5
2025-07-15 04:43:57,721 - app.services.queue_manager - INFO - Added item ITM_001 to kitchen KITCHEN_A queue
2025-07-15 04:43:57,721 - app.services.queue_manager - INFO - Added item ITM_004 to kitchen KITCHEN_B queue
2025-07-15 04:43:57,722 - app.services.queue_manager - INFO - Added item ITM_007 to kitchen KITCHEN_C queue
2025-07-15 04:43:57,722 - app.api.orders - INFO - Created order ORD_A72D7AF5 with 3 items
2025-07-15 04:45:04,304 - app.services.ai_tools - INFO - Analyzed order ORD_A72D7AF5: 3 kitchens, 58 min total
2025-07-15 04:45:04,318 - app.services.ai_tools - INFO - Kitchen load assessment: 3/9 capacity used
2025-07-15 04:45:04,319 - app.services.ai_tools - INFO - Starvation check for order ORD_A72D7AF5: 0 starving items
2025-07-15 04:45:04,325 - app.services.ai_tools - ERROR - Error optimizing schedule for order ORD_A72D7AF5: unsupported operand type(s) for -: 'str' and 'datetime.datetime'
2025-07-15 04:45:04,326 - app.services.ai_agent - WARNING - Using fallback scheduling for order ORD_A72D7AF5
2025-07-15 04:45:04,327 - app.services.ai_agent - INFO - AI optimization completed for order ORD_A72D7AF5
2025-07-15 04:45:04,328 - app.services.ai_agent - INFO - Reoptimized 1 pending orders
2025-07-15 04:45:23,972 - app.services.performance_learning - INFO - Identified 0 bottlenecks
