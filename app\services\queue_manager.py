"""
Kitchen Queue Manager for the Smart Kitchen Queue Management System.
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import threading

from app.models import QueueItem, ItemStatus, KitchenStatusModel, PriorityLevel
from app.services.excel_service import ExcelDataService

logger = logging.getLogger(__name__)


class KitchenQueueManager:
    """Manages queues for all kitchens in the system."""
    
    def __init__(self, excel_service: ExcelDataService):
        """Initialize the kitchen queue manager."""
        self.excel_service = excel_service
        self._kitchen_queues: Dict[str, List[QueueItem]] = defaultdict(list)
        self._kitchen_configs: Dict = {}
        self._lock = threading.RLock()  # Thread-safe operations
        self._load_kitchen_configs()
    
    def _load_kitchen_configs(self):
        """Load kitchen configurations from Excel."""
        try:
            self._kitchen_configs = self.excel_service.load_kitchen_config()
            logger.info(f"Loaded configurations for {len(self._kitchen_configs)} kitchens")
        except Exception as e:
            logger.error(f"Failed to load kitchen configurations: {e}")
            raise
    
    def get_kitchen_current_load(self, kitchen_id: str) -> int:
        """Get the current load (number of active items) for a kitchen."""
        with self._lock:
            if kitchen_id not in self._kitchen_configs:
                logger.warning(f"Kitchen {kitchen_id} not found in configuration")
                return 0
            
            active_items = [
                item for item in self._kitchen_queues[kitchen_id]
                if item.status in [ItemStatus.QUEUED, ItemStatus.IN_PROGRESS]
            ]
            
            return len(active_items)
    
    def add_item_to_queue(self, kitchen_id: str, item: QueueItem) -> bool:
        """Add an item to a kitchen's queue."""
        with self._lock:
            if kitchen_id not in self._kitchen_configs:
                logger.error(f"Kitchen {kitchen_id} not found in configuration")
                return False
            
            # Check capacity
            if not self.check_kitchen_capacity(kitchen_id):
                logger.warning(f"Kitchen {kitchen_id} is at full capacity")
                return False
            
            # Add to queue
            item.kitchen_id = kitchen_id
            item.status = ItemStatus.QUEUED
            
            # Insert based on priority and scheduled start time
            self._insert_item_by_priority(kitchen_id, item)
            
            logger.info(f"Added item {item.item_id} to kitchen {kitchen_id} queue")
            return True
    
    def _insert_item_by_priority(self, kitchen_id: str, new_item: QueueItem):
        """Insert item into queue based on priority and scheduled time."""
        queue = self._kitchen_queues[kitchen_id]
        
        # Priority order: EMERGENCY > HIGH > NORMAL > LOW
        priority_order = {
            PriorityLevel.EMERGENCY: 0,
            PriorityLevel.HIGH: 1,
            PriorityLevel.NORMAL: 2,
            PriorityLevel.LOW: 3
        }
        
        # Find insertion point
        insert_index = len(queue)
        for i, item in enumerate(queue):
            if item.status == ItemStatus.COMPLETED:
                continue
                
            # Compare priority first
            if priority_order[new_item.priority_level] < priority_order[item.priority_level]:
                insert_index = i
                break
            elif priority_order[new_item.priority_level] == priority_order[item.priority_level]:
                # Same priority, compare scheduled start time
                if new_item.scheduled_start < item.scheduled_start:
                    insert_index = i
                    break
        
        queue.insert(insert_index, new_item)
    
    def remove_completed_item(self, kitchen_id: str, item_id: str) -> bool:
        """Mark an item as completed and remove from active queue."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                logger.warning(f"Kitchen {kitchen_id} has no queue")
                return False
            
            for item in self._kitchen_queues[kitchen_id]:
                if item.item_id == item_id and item.status != ItemStatus.COMPLETED:
                    item.status = ItemStatus.COMPLETED
                    item.actual_completion = datetime.now()
                    
                    logger.info(f"Marked item {item_id} as completed in kitchen {kitchen_id}")
                    return True
            
            logger.warning(f"Item {item_id} not found in kitchen {kitchen_id} queue")
            return False
    
    def get_estimated_completion_time(self, kitchen_id: str) -> datetime:
        """Get estimated completion time for all items in a kitchen's queue."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                return datetime.now()
            
            current_time = datetime.now()
            estimated_time = current_time
            
            # Calculate based on queued and in-progress items
            active_items = [
                item for item in self._kitchen_queues[kitchen_id]
                if item.status in [ItemStatus.QUEUED, ItemStatus.IN_PROGRESS]
            ]
            
            # Sort by scheduled start time
            active_items.sort(key=lambda x: x.scheduled_start)
            
            for item in active_items:
                if item.status == ItemStatus.IN_PROGRESS:
                    # Item is already in progress, use its estimated completion
                    estimated_time = max(estimated_time, item.estimated_completion)
                else:
                    # Item is queued, add its prep time
                    estimated_time = max(estimated_time, item.scheduled_start) + timedelta(minutes=item.prep_time)
            
            return estimated_time
    
    def check_kitchen_capacity(self, kitchen_id: str) -> bool:
        """Check if a kitchen has available capacity."""
        if kitchen_id not in self._kitchen_configs:
            return False
        
        capacity = self._kitchen_configs[kitchen_id]['capacity']
        current_load = self.get_kitchen_current_load(kitchen_id)
        
        return current_load < capacity
    
    def get_kitchen_status(self, kitchen_id: str) -> Optional[KitchenStatusModel]:
        """Get comprehensive status for a kitchen."""
        with self._lock:
            if kitchen_id not in self._kitchen_configs:
                return None
            
            config = self._kitchen_configs[kitchen_id]
            current_queue = self._kitchen_queues[kitchen_id].copy()
            current_load = self.get_kitchen_current_load(kitchen_id)
            available_slots = config['capacity'] - current_load
            next_available_time = self.get_estimated_completion_time(kitchen_id)
            
            return KitchenStatusModel(
                kitchen_id=kitchen_id,
                kitchen_name=config['kitchen_name'],
                current_queue=current_queue,
                capacity=config['capacity'],
                current_load=current_load,
                available_slots=available_slots,
                next_available_time=next_available_time,
                status=config['status'],
                specialization=config['specialization']
            )

    def get_all_kitchen_statuses(self) -> List[KitchenStatusModel]:
        """Get status for all kitchens."""
        statuses = []
        for kitchen_id in self._kitchen_configs.keys():
            status = self.get_kitchen_status(kitchen_id)
            if status:
                statuses.append(status)
        return statuses

    def get_queue_items_by_order(self, order_id: str) -> List[QueueItem]:
        """Get all queue items for a specific order."""
        with self._lock:
            items = []
            for kitchen_queue in self._kitchen_queues.values():
                for item in kitchen_queue:
                    if item.order_id == order_id:
                        items.append(item)
            return items

    def update_item_status(self, kitchen_id: str, item_id: str, status: ItemStatus,
                          actual_start: Optional[datetime] = None) -> bool:
        """Update the status of a specific item."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                return False

            for item in self._kitchen_queues[kitchen_id]:
                if item.item_id == item_id:
                    item.status = status
                    if actual_start:
                        item.actual_start = actual_start
                    if status == ItemStatus.COMPLETED:
                        item.actual_completion = datetime.now()

                    logger.info(f"Updated item {item_id} status to {status}")
                    return True

            return False

    def get_next_available_slot(self, kitchen_id: str) -> datetime:
        """Get the next available time slot for a kitchen."""
        with self._lock:
            if not self.check_kitchen_capacity(kitchen_id):
                # Kitchen is at capacity, return estimated completion time
                return self.get_estimated_completion_time(kitchen_id)
            else:
                # Kitchen has capacity, can start immediately
                return datetime.now()

    def reorder_queue_by_priority(self, kitchen_id: str):
        """Reorder a kitchen's queue based on current priorities."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                return

            queue = self._kitchen_queues[kitchen_id]

            # Separate completed items from active ones
            completed_items = [item for item in queue if item.status == ItemStatus.COMPLETED]
            active_items = [item for item in queue if item.status != ItemStatus.COMPLETED]

            # Sort active items by priority and scheduled time
            priority_order = {
                PriorityLevel.EMERGENCY: 0,
                PriorityLevel.HIGH: 1,
                PriorityLevel.NORMAL: 2,
                PriorityLevel.LOW: 3
            }

            active_items.sort(key=lambda x: (
                priority_order[x.priority_level],
                x.scheduled_start
            ))

            # Rebuild queue
            self._kitchen_queues[kitchen_id] = active_items + completed_items

            logger.info(f"Reordered queue for kitchen {kitchen_id}")

    def clear_completed_items(self, kitchen_id: str, older_than_hours: int = 24):
        """Clear completed items older than specified hours."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                return

            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            original_count = len(self._kitchen_queues[kitchen_id])

            self._kitchen_queues[kitchen_id] = [
                item for item in self._kitchen_queues[kitchen_id]
                if not (item.status == ItemStatus.COMPLETED and
                       item.actual_completion and
                       item.actual_completion < cutoff_time)
            ]

            cleared_count = original_count - len(self._kitchen_queues[kitchen_id])
            if cleared_count > 0:
                logger.info(f"Cleared {cleared_count} completed items from kitchen {kitchen_id}")

    def get_kitchen_efficiency_metrics(self, kitchen_id: str) -> Dict:
        """Get efficiency metrics for a kitchen."""
        with self._lock:
            if kitchen_id not in self._kitchen_queues:
                return {}

            completed_items = [
                item for item in self._kitchen_queues[kitchen_id]
                if item.status == ItemStatus.COMPLETED and item.actual_completion
            ]

            if not completed_items:
                return {"total_completed": 0}

            total_items = len(completed_items)
            total_scheduled_time = sum(item.prep_time for item in completed_items)
            total_actual_time = sum(
                (item.actual_completion - item.actual_start).total_seconds() / 60
                for item in completed_items
                if item.actual_start and item.actual_completion
            )

            avg_delay = sum(
                max(0, (item.actual_completion - item.estimated_completion).total_seconds() / 60)
                for item in completed_items
                if item.actual_completion and item.estimated_completion
            ) / total_items if total_items > 0 else 0

            return {
                "total_completed": total_items,
                "avg_scheduled_time": total_scheduled_time / total_items if total_items > 0 else 0,
                "avg_actual_time": total_actual_time / total_items if total_items > 0 else 0,
                "avg_delay_minutes": avg_delay,
                "efficiency_ratio": (total_scheduled_time / total_actual_time) if total_actual_time > 0 else 0
            }
