# Smart Kitchen Queue Management System

An intelligent kitchen queue management system that optimizes food preparation across multiple kitchens using AI-driven prioritization.

## Features

- **AI-Driven Scheduling**: Uses Ollama LLM for intelligent order prioritization
- **Multi-Kitchen Support**: Manages 3 specialized kitchens with different capacities
- **Anti-Starvation System**: Prevents items from being perpetually delayed
- **Historical Learning**: Learns from past performance to improve scheduling
- **Real-time Updates**: Provides live status updates and queue management
- **Excel Configuration**: Easy configuration through Excel spreadsheets

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup Ollama**
   ```bash
   # Install Ollama (visit https://ollama.ai for installation instructions)
   ollama pull llama2
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the Application**
   ```bash
   python main.py
   ```

5. **Access API Documentation**
   - Open http://localhost:8000/docs for interactive API documentation

## Project Structure

```
kds/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py              # Configuration management
│   ├── models/                # Pydantic data models
│   ├── services/              # Business logic services
│   ├── api/                   # API endpoints
│   └── utils/                 # Utility functions
├── data/
│   └── kitchen_config.xlsx    # Kitchen and menu configuration
├── tests/                     # Test suite
├── logs/                      # Application logs
├── requirements.txt
├── .env
└── README.md
```

## API Endpoints

### Order Management
- `POST /api/orders` - Submit new order
- `GET /api/orders/{order_id}` - Get order status
- `PUT /api/orders/{order_id}/cancel` - Cancel order

### Kitchen Management
- `GET /api/kitchens/status` - Get all kitchen statuses
- `GET /api/kitchens/{kitchen_id}` - Get specific kitchen status
- `PUT /api/kitchens/{kitchen_id}/complete/{item_id}` - Mark item as completed

### Real-time Updates
- `GET /api/realtime/queue` - Get current queue status
- `GET /api/realtime/performance` - Get performance metrics

## Configuration

The system uses Excel files for configuration. See `data/kitchen_config.xlsx` for the required format with sheets:
- `kitchens` - Kitchen configurations
- `items` - Menu items and preparation times
- `historical_performance` - Historical performance data
- `kitchen_load_patterns` - Load pattern analysis

## Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app tests/
```

## License

MIT License
