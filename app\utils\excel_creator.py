"""
Utility to create sample Excel configuration file for the kitchen system.
"""

import pandas as pd
from datetime import datetime, timedelta
import os


def create_sample_excel(file_path: str):
    """Create a sample Excel file with all required sheets and data."""
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        
        # Sheet 1: kitchens
        kitchens_data = {
            'kitchen_id': ['KITCHEN_A', 'KITCHEN_B', 'KITCHEN_C'],
            'kitchen_name': ['Grill Station', 'Pasta Station', 'Dessert Station'],
            'capacity': [3, 2, 4],
            'specialization': ['Grilled Items', 'Pasta & Rice', 'Desserts & Beverages'],
            'status': ['active', 'active', 'active']
        }
        kitchens_df = pd.DataFrame(kitchens_data)
        kitchens_df.to_excel(writer, sheet_name='kitchens', index=False)
        
        # Sheet 2: items
        items_data = {
            'item_id': [
                'ITM_001', 'ITM_002', 'ITM_003', 'ITM_004', 'ITM_005', 'ITM_006',
                'ITM_007', 'ITM_008', 'ITM_009', 'ITM_010', 'ITM_011', 'ITM_012'
            ],
            'item_name': [
                'Grilled Chicken', 'Beef Steak', 'Grilled Salmon', 'Spaghetti Bolognese',
                'Chicken Alfredo', 'Mushroom Risotto', 'Chocolate Cake', 'Tiramisu',
                'Fresh Juice', 'Coffee', 'Ice Cream', 'Fruit Salad'
            ],
            'kitchen_id': [
                'KITCHEN_A', 'KITCHEN_A', 'KITCHEN_A', 'KITCHEN_B',
                'KITCHEN_B', 'KITCHEN_B', 'KITCHEN_C', 'KITCHEN_C',
                'KITCHEN_C', 'KITCHEN_C', 'KITCHEN_C', 'KITCHEN_C'
            ],
            'prep_time_minutes': [15, 20, 18, 18, 16, 22, 25, 30, 5, 3, 2, 8],
            'difficulty_level': [
                'medium', 'high', 'medium', 'high', 'medium', 'high',
                'high', 'high', 'low', 'low', 'low', 'low'
            ],
            'available': [True] * 12
        }
        items_df = pd.DataFrame(items_data)
        items_df.to_excel(writer, sheet_name='items', index=False)
        
        # Sheet 3: historical_performance (sample data for last 30 days)
        historical_data = []
        base_date = datetime.now() - timedelta(days=30)
        
        for i in range(100):  # 100 sample records
            date = base_date + timedelta(days=i % 30)
            kitchen_id = ['KITCHEN_A', 'KITCHEN_B', 'KITCHEN_C'][i % 3]
            item_id = items_data['item_id'][i % len(items_data['item_id'])]
            scheduled_time = items_data['prep_time_minutes'][i % len(items_data['prep_time_minutes'])]
            actual_time = scheduled_time + (i % 5 - 2)  # Some variation
            delay = max(0, actual_time - scheduled_time)
            
            historical_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'kitchen_id': kitchen_id,
                'item_id': item_id,
                'actual_prep_time': actual_time,
                'scheduled_prep_time': scheduled_time,
                'delay_minutes': delay,
                'kitchen_load': (i % 3) + 1,
                'order_id': f'ORD_{i+1:03d}'
            })
        
        historical_df = pd.DataFrame(historical_data)
        historical_df.to_excel(writer, sheet_name='historical_performance', index=False)
        
        # Sheet 4: kitchen_load_patterns
        load_patterns_data = {
            'hour': list(range(24)),
            'kitchen_a_avg_load': [
                0.5, 0.3, 0.2, 0.2, 0.3, 0.5, 0.8, 1.2, 1.5, 1.8,
                2.0, 2.2, 2.5, 2.3, 2.0, 1.8, 1.5, 1.3, 1.0, 0.8,
                0.6, 0.5, 0.4, 0.3
            ],
            'kitchen_b_avg_load': [
                0.3, 0.2, 0.1, 0.1, 0.2, 0.3, 0.5, 0.8, 1.1, 1.4,
                1.6, 1.8, 1.9, 1.7, 1.5, 1.3, 1.1, 0.9, 0.7, 0.5,
                0.4, 0.3, 0.3, 0.2
            ],
            'kitchen_c_avg_load': [
                0.8, 0.5, 0.3, 0.2, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0,
                2.5, 3.0, 3.2, 3.0, 2.8, 2.5, 2.0, 1.5, 1.2, 1.0,
                0.8, 0.7, 0.6, 0.5
            ]
        }
        load_patterns_df = pd.DataFrame(load_patterns_data)
        load_patterns_df.to_excel(writer, sheet_name='kitchen_load_patterns', index=False)
    
    print(f"Sample Excel file created at: {file_path}")


if __name__ == "__main__":
    create_sample_excel("data/kitchen_config.xlsx")
